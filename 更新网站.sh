#!/bin/bash

# 简化版网站更新脚本
# 用途: 快速更新网站内容

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
WEB_ROOT="/var/www/portfolio"
BACKUP_DIR="/var/backups/portfolio"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 验证文件
validate_files() {
    log_step "验证网站文件..."
    
    required_files=("index.html" "protfolio.html")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_info "文件验证通过"
}

# 备份当前网站
backup_current() {
    log_step "备份当前网站..."
    
    if [[ -d $WEB_ROOT ]] && [[ "$(ls -A $WEB_ROOT 2>/dev/null)" ]]; then
        backup_name="update_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR/$backup_name"
        cp -r $WEB_ROOT/* "$BACKUP_DIR/$backup_name/" 2>/dev/null || true
        log_info "备份完成: $BACKUP_DIR/$backup_name"
        
        # 保留最近5个备份
        cd $BACKUP_DIR
        ls -t | tail -n +6 | xargs -r rm -rf
    else
        log_warn "网站目录为空，跳过备份"
    fi
}

# 更新网站文件
update_files() {
    log_step "更新网站文件..."
    
    # 确保目录存在
    mkdir -p $WEB_ROOT
    
    # 复制新文件
    cp -r ./* $WEB_ROOT/ 2>/dev/null || true
    
    # 排除脚本文件
    rm -f $WEB_ROOT/*.sh $WEB_ROOT/*.bat 2>/dev/null || true
    
    # 设置正确权限
    chown -R www-data:www-data $WEB_ROOT 2>/dev/null || chown -R nginx:nginx $WEB_ROOT
    find $WEB_ROOT -type d -exec chmod 755 {} \;
    find $WEB_ROOT -type f -exec chmod 644 {} \;
    
    log_info "文件更新完成"
}

# 验证更新
verify_update() {
    log_step "验证更新..."
    
    # 检查关键文件
    if [[ -f "$WEB_ROOT/index.html" ]]; then
        log_info "✓ index.html 存在"
    else
        log_error "✗ index.html 不存在"
        exit 1
    fi
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        log_info "✓ Nginx运行正常"
    else
        log_warn "Nginx未运行，尝试启动..."
        systemctl start nginx
    fi
    
    # 重新加载Nginx配置
    nginx -t && systemctl reload nginx
    log_info "✓ Nginx配置已重新加载"
}

# 显示更新信息
show_update_info() {
    echo ""
    echo -e "${GREEN}======================================${NC}"
    echo -e "${GREEN}         网站更新完成！${NC}"
    echo -e "${GREEN}======================================${NC}"
    echo ""
    echo "更新信息:"
    echo "- 更新时间: $(date)"
    echo "- 网站目录: $WEB_ROOT"
    echo "- 备份位置: $BACKUP_DIR"
    echo ""
    echo "网站文件:"
    ls -la $WEB_ROOT | head -10
    echo ""
    echo "访问网站:"
    echo "- 请通过浏览器访问您的网站"
    echo "- 按Ctrl+F5强制刷新页面"
    echo ""
}

# 显示帮助
show_help() {
    echo "网站更新脚本"
    echo ""
    echo "功能:"
    echo "- 自动备份当前网站"
    echo "- 更新网站文件"
    echo "- 设置正确权限"
    echo "- 重新加载Nginx"
    echo ""
    echo "使用方法:"
    echo "  $0              # 更新网站"
    echo "  $0 --help       # 显示帮助"
    echo ""
    echo "使用步骤:"
    echo "1. 将新的网站文件放在脚本同目录"
    echo "2. 运行此脚本进行更新"
    echo "3. 访问网站验证更新结果"
}

# 主函数
main() {
    echo -e "${BLUE}开始更新网站...${NC}"
    echo ""
    
    check_root
    validate_files
    backup_current
    update_files
    verify_update
    show_update_info
    
    log_info "网站更新完成！"
}

# 处理参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
