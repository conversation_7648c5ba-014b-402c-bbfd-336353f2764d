# 单元管理展开操作功能说明

## 功能概述

单元管理页面的"单元层级结构"模块中包含了完整的展开操作功能，支持多种智能展开模式，提升用户体验。

## 实现的功能

### 1. 展开操作下拉菜单
位置：左侧单元层级结构卡片 → 展开操作按钮（下拉菜单）

包含以下选项：
- 展开全部：展开树形结构中的所有节点
- 展开到建筑级：只展开到建筑级别（第一层）
- 展开到楼层级：展开到楼层级别（第二层）
- 展开异常单元：智能展开包含异常状态的单元及其路径

### 2. 收起操作
- 收起全部：收起所有已展开的节点

### 3. 刷新功能
- 刷新：重新加载树形数据

## 使用方式

### 1. 基本操作
1. 点击"展开操作"下拉按钮
2. 选择需要的展开模式：
   - "展开全部"：查看完整的树形结构
   - "展开到建筑级"：快速查看所有建筑
   - "展开到楼层级"：查看建筑和楼层结构
   - "展开异常单元"：快速定位有问题的单元

### 2. 快速收起
- 点击"收起全部"按钮可以快速收起所有节点

### 3. 数据刷新
- 点击"刷新"按钮重新加载最新的树形数据

## 用户反馈

每个操作都会显示相应的成功消息：
- "已展开所有节点"
- "已展开到建筑级"
- "已展开到楼层级"
- "已展开异常单元路径" 或 "未发现异常单元"
- "已收起所有节点"
- "树形数据刷新成功"

## 技术实现特点

1. **递归遍历算法**：支持任意层级的树形结构
2. **智能路径查找**：异常单元展开时自动展开完整路径
3. **层级控制**：可精确控制展开到指定层级
4. **去重处理**：避免重复节点导致的性能问题
5. **用户友好**：每个操作都有清晰的反馈提示

## 扩展性

当前实现支持：
- 任意层级的树形结构
- 自定义状态类型的异常检测
- 可配置的层级展开深度
- 可扩展的操作类型

## 浏览器兼容性

- 支持所有现代浏览器
- 使用 Vue 3 + Element Plus 组件
- 响应式设计，支持移动端访问 