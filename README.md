# 个人简历展示网站项目

## 🎯 项目概述

这是一个专业的个人简历展示网站，包含22个原型设计案例、4个AI智能体应用和2个在线演示系统。支持多种部署方式，特别优化了Linux服务器的简化部署方案。

## 📁 项目文件结构

```
├── index.html              # 网站主页
├── protfolio.html          # 作品集详情页
├── 个人简历-张一钦.pdf      # PDF简历下载
├── 原型链接集合.md          # 22个原型的详细链接
├── 简化部署.sh             # Linux服务器一键部署脚本
├── 更新网站.sh             # 网站内容更新脚本
├── 简化管理.sh             # 网站管理工具
└── README.md               # 项目说明（本文件）
```

## 🌟 网站内容展示

### 📋 项目分类（22个原型）

**☁️ 云监控系统（6个）**
- GPU事件监控、RDMA网络拓扑、EPC事件收敛
- IB网络详情、监控图表优化、告警优化

**🔧 内部监控系统（6个）**
- Pingmesh探测、SLI优化、事件中心
- 北斗告警、日志告警、查询优化

**⚙️ 运维系统（4个）**
- 服务可观测、告警2.0升级、移动端告警屏蔽

**🤖 AI生成前端系统（2个）**
- 能源管理系统：http://**************:3003/
- 慢性病管理系统：http://**************:5173/

**🤖 AI智能体（4个）**
- 医疗问诊助手、企业办公助手
- 医疗分诊助手、滴滴计费解答

## 🚀 部署方式

### 方式1: Linux服务器部署（推荐）

**特点**: 简单、快速、无需SSL证书
```bash
# 1. 上传文件到服务器
scp -r ./* root@your-server-ip:/root/portfolio/

# 2. 登录服务器执行部署
ssh root@your-server-ip
cd /root/portfolio
chmod +x *.sh
sudo ./简化部署.sh
```

**访问**: `http://your-server-ip` 或 `http://your-domain.com`

### 方式2: GitHub Pages
1. 上传到GitHub仓库
2. 启用Pages功能
3. 访问：`https://username.github.io/repository-name`

### 方式3: Vercel/Netlify
1. 导入GitHub仓库
2. 自动部署
3. 获得自定义域名

## 🛠️ Linux服务器管理

### 日常维护
```bash
# 更新网站内容
sudo ./更新网站.sh

# 网站管理（交互式菜单）
sudo ./简化管理.sh
```

### 管理功能
- ✅ 更新网站内容
- ✅ 查看网站状态
- ✅ 查看访问日志
- ✅ 备份网站数据
- ✅ 重启Nginx服务
- ✅ 查看系统信息

### 目录结构
```
/var/www/portfolio/          # 网站根目录
/var/backups/portfolio/      # 自动备份目录
/var/log/portfolio/          # 网站日志目录
```

## 🔧 技术特性

### 网站特性
- **响应式设计** - 适配所有设备
- **现代化UI** - 渐变色彩、卡片布局
- **快速加载** - Gzip压缩、静态缓存
- **SEO友好** - 优化的HTML结构

### 部署特性
- **3分钟部署** - 一键自动化脚本
- **无需SSL** - 简化配置，降低复杂度
- **自动备份** - 更新前自动备份
- **日志监控** - 访问日志和错误日志

## 📊 为什么选择简化部署？

### ✅ 适合个人网站的原因
- **静态内容** - 无用户登录，无敏感数据
- **展示用途** - 主要用于简历和作品展示
- **快速上线** - 无需复杂的SSL证书配置
- **成本低廉** - 可用IP直接访问，无需域名

### 🆚 与复杂版对比
| 功能 | 简化版 | 复杂版 |
|------|--------|--------|
| 部署时间 | 3分钟 | 30分钟 |
| SSL证书 | 无需 | 需要 |
| 域名要求 | 可选 | 必需 |
| 维护复杂度 | 简单 | 复杂 |

## 📞 个人信息

- **姓名**: 张一钦
- **邮箱**: <EMAIL>
- **电话**: 18062604712
- **微信**: 同手机号

### 职业背景
- **6年技术研发** + **4年产品管理**
- **复合型背景** - 技术+产品双重视角
- **AI应用实践** - 大语言模型产品化经验

### 求职方向
- 产品经理 / 技术产品经理 / AI产品经理

## 🎯 项目亮点

1. **22个真实原型** - 每个都可直接访问体验
2. **4个AI智能体** - 展示AI产品化能力
3. **2个完整系统** - AI辅助开发的实际成果
4. **简化部署方案** - 3分钟上线，适合个人网站
5. **完整管理工具** - 便于后续维护和更新

---

**这个项目展示了从技术开发到产品管理的完整能力体系，以及在云监控、运维管理、AI应用等领域的深度实践经验。**

### 更新项目链接
修改项目卡片中的链接地址：

```html
<a href="你的项目链接" class="project-link" target="_blank">访问项目 →</a>
```

### 替换文件
- 替换 `个人简历-张一钦.pdf` 为你的简历文件
- 替换 `原型链接合集.docx` 为你的作品集文档

## 🎨 样式自定义

### 修改主题色彩
在CSS变量中修改颜色：

```css
:root {
    --primary-color: #3498db;    /* 主色调 */
    --secondary-color: #2c3e50;  /* 次要色 */
    --accent-color: #e74c3c;     /* 强调色 */
}
```

### 添加新的项目分类
复制现有的项目分类HTML结构，修改标题和内容即可。

## 📱 响应式设计

网站已适配移动端，在手机、平板、桌面端都有良好的显示效果。

## 🔗 在简历中使用

部署完成后，可以在简历中添加以下内容：

```
个人作品集网站：https://你的域名
- 包含详细的项目案例和原型设计
- 展示云监控、运维管理、AI应用等领域经验
- 提供在线演示和文档下载
```

## 📞 技术支持

如需帮助或有问题，请联系：
- 邮箱：<EMAIL>
- 电话：18062604712

## 📄 许可证

本项目仅供个人简历展示使用。
