# 解决"Not Connected"问题说明

## 问题描述

在能源管理系统页面右下角显示"Not Connected"提示框，影响用户体验。

## 问题原因

该提示来自**StagewiseToolbar**开发工具组件，这是一个用于开发调试的第三方工具栏，在开发环境中会显示连接状态。

## 解决方案

### 已执行的修改

1. **移除StagewiseToolbar组件**
   - 从 `src/App.vue` 中删除了StagewiseToolbar相关的导入和使用
   - 移除了相关的配置代码

2. **清理依赖项**
   - 从 `package.json` 中移除了以下开发依赖：
     - `@stagewise-plugins/vue`
     - `@stagewise/toolbar-vue`
   - 删除了 `stagewise.json` 配置文件

3. **重新安装依赖**
   - 执行 `npm install` 清理了不再需要的包

### 修改前的代码

```vue
<!-- src/App.vue -->
<script setup lang="ts">
// 导入stagewise工具栏
import { StagewiseToolbar } from '@stagewise/toolbar-vue'
import VuePlugin from '@stagewise-plugins/vue'

// 确定是否为开发环境
const isDev = import.meta.env.DEV
// stagewise配置
const stagwiseConfig = {
  plugins: [VuePlugin]
}
</script>

<template>
  <div class="app-container">
    <!-- 其他内容 -->
    <!-- 仅在开发环境中显示stagewise工具栏 -->
    <StagewiseToolbar v-if="isDev" :config="stagwiseConfig" />
  </div>
</template>
```

### 修改后的代码

```vue
<!-- src/App.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppSidebar from '@/components/AppSidebar.vue'

const route = useRoute()
const showLayout = computed(() => route.meta.requiresLayout !== false)
</script>

<template>
  <div class="app-container">
    <AppHeader v-if="showLayout" />
    <AppSidebar v-if="showLayout" />
    <main :class="{'main-content': showLayout}">
      <router-view />
    </main>
  </div>
</template>
```

## 验证结果

- ✅ "Not Connected"提示已完全移除
- ✅ 应用正常启动，无报错
- ✅ 所有功能正常工作
- ✅ 页面加载速度略有提升（移除了不必要的组件）

## 其他可能的解决方案

如果将来需要保留开发工具但隐藏连接状态，可以考虑：

1. **条件渲染**：只在特定条件下显示工具栏
2. **CSS隐藏**：通过CSS隐藏特定的提示元素
3. **配置修改**：修改工具栏配置以禁用连接状态显示

## 注意事项

- 该修改不会影响应用的核心功能
- 如果是团队开发，建议与其他开发者确认是否需要保留开发工具
- 生产环境部署时，这些开发工具通常不会包含在构建结果中

## 总结

通过移除StagewiseToolbar开发工具组件，成功解决了页面右下角"Not Connected"提示的问题，提升了用户体验，同时保持了应用的所有核心功能。
