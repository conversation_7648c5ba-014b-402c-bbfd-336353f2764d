### 1. **云监控相关需求**

\1. 云监控GPU事件管理  https://app.axure.cloud/app/project/2uwzkw/preview/xrxh9f

\2. EPC事件管理收敛 <https://app.axure.cloud/app/project/sg0t4a/preview/1sw5n5>

\3. IB网络事件详情 <https://app.axure.cloud/app/project/p1ibh7/preview/k4d9dj>

\4. RDMA网络监控 <https://app.axure.cloud/app/project/gtkmzz/preview/d89m0k>

\5. 四维图新告警优化 https://app.axure.cloud/app/project/htvyoi/preview/zv65dj

\6. UFM告警 <https://app.axure.cloud/app/project/utot3l/preview/d89m0k>

\7. 监控图表优化 <https://app.axure.cloud/app/project/5u9yr0/preview/27kyhq>

### 2. **内部监控相关需求**

\1. Pingmesh探测功能 <https://app.axure.cloud/app/project/vs49h5/preview/4nyd98>

\2. SLI指标优化 <https://app.axure.cloud/app/project/iqenno/preview/s51kt0>

\3. 事件中心管理 <https://app.axure.cloud/app/project/o3eszm/preview/ba2nc2>

\4. 北斗事件告警配置 <https://app.axure.cloud/app/project/j1ncfw/preview/ea04a9>

\5. 北斗菜单系统优化 <https://app.axure.cloud/app/project/t2n1eu/preview/i2ixdv>

\6. 告警服务标准化改造 <https://app.axure.cloud/app/project/8m6194/preview/ulyx7g>

\7. 日志告警-定时任务 <https://app.axure.cloud/app/project/vzmhrs/preview/q2eutl>

\8. 日志告警支持告警聚合功能 <https://app.axure.cloud/app/project/nxrqos/preview/dm1i8j>

\9. 服务树支持批量操作  <https://app.axure.cloud/app/project/srbgds/preview/mvayzb>

\10. SLI服务树告警配置 <https://app.axure.cloud/app/project/fjl9p5/preview/29jd7y>

\11. 北斗阈值告警界面改版 <https://app.axure.cloud/app/project/duxoq8/preview/rm4er5>

\12. 日志查询操作流程优化 （使用MasterGo生成）https://mastergo.com/goto/MrYwaTRD?page_id=M&layer_id=1:4555&proto=1&shared=true

### 3. **运维系统相关需求**

\1. 服务可观测项目 <https://app.axure.cloud/app/project/fesc2e/preview/y28ejs>

\2. 可观测支持公网及专线 <https://app.axure.cloud/app/project/orypum/preview/uui75l>

\3. 报警2.0告警升级 <https://app.axure.cloud/app/project/l75qp1/preview/wgz5hs>

\4. 手机端告警快速屏蔽 <https://app.axure.cloud/app/project/qxstdv/preview/yixzlx>

### 4. **能源管理系统前端界面** 

该系统前端界面为本人使用vscode + Augment,通过AI自动生成的前端演示界面，已部署到本人的118.178.228.37服务器上，欢迎您登陆体验。

URL：<http://118.178.228.37:3003/>

用户名：admin 

密码：123456

### 5. **慢性病管理系统前端界面** 

该系统前端界面为本人使用vscode + Augment,通过AI自动生成的前端演示界面，已部署到本人的118.178.228.37服务器上，欢迎您登陆体验。

URL：http://118.178.228.37:5173/

用户名：admin 

密码：123456

### 6. **扣子智能体实现的小需求**

\1. 医疗问诊助手 <https://www.coze.cn/store/agent/7546178287161737279?bot_id=true> 

\2. 企业办公助手 <https://www.coze.cn/store/agent/7544664687280078882?bot_id=true> 

\3. 医疗分诊助手 <https://www.coze.cn/store/agent/7546178287161737279?bot_id=true> 

\4. 滴滴计费解答 https://www.coze.cn/store/agent/7543558438652379146?bot_id=true

 

 