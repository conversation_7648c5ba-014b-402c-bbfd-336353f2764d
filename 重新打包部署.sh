#!/bin/bash

# 重新打包部署脚本 - 解决中文文件名编码问题
# 用途: 将中文文件名转换为英文，避免服务器乱码

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "========================================================"
    echo "           重新打包部署工具"
    echo "           Repackage Deploy Tool"
    echo "========================================================"
    echo -e "${NC}"
}

# 创建文件名映射
create_safe_filenames() {
    log_step "创建安全的文件名映射..."
    
    # 创建临时目录
    TEMP_DIR="./temp_deploy_$(date +%s)"
    mkdir -p "$TEMP_DIR"
    
    # 文件名映射
    declare -A filename_map=(
        ["个人简历-张一钦.pdf"]="resume-zhang-yiqin.pdf"
        ["原型链接合集.docx"]="prototype-links-collection.docx"
        ["原型链接集合.md"]="prototype-links.md"
        ["更新网站.sh"]="update-website.sh"
        ["简化管理.sh"]="simple-management.sh"
        ["简化部署.sh"]="simple-deploy.sh"
        ["1. 能源管理系统"]="1-energy-management-system"
        ["2.金山云需求集"]="2-kingsoft-cloud-requirements"
        ["3.优维科技"]="3-youwei-tech"
        ["4.众邦银行"]="4-zhongbang-bank"
    )
    
    # 复制并重命名文件
    log_info "复制文件到临时目录..."
    
    # 复制基本文件
    for file in index.html protfolio.html README.md; do
        if [[ -f "$file" ]]; then
            cp "$file" "$TEMP_DIR/"
            log_info "✓ 复制: $file"
        fi
    done
    
    # 处理需要重命名的文件
    for original in "${!filename_map[@]}"; do
        safe_name="${filename_map[$original]}"
        if [[ -f "$original" ]]; then
            cp "$original" "$TEMP_DIR/$safe_name"
            log_info "✓ 重命名: $original -> $safe_name"
        elif [[ -d "$original" ]]; then
            cp -r "$original" "$TEMP_DIR/$safe_name"
            log_info "✓ 重命名目录: $original -> $safe_name"
        fi
    done
    
    # 更新HTML文件中的链接
    update_html_links "$TEMP_DIR"
    
    echo "$TEMP_DIR"
}

# 更新HTML文件中的链接
update_html_links() {
    local temp_dir="$1"
    
    log_step "更新HTML文件中的链接..."
    
    # 更新index.html中的链接
    if [[ -f "$temp_dir/index.html" ]]; then
        sed -i 's/个人简历-张一钦\.pdf/resume-zhang-yiqin.pdf/g' "$temp_dir/index.html"
        sed -i 's/原型链接合集\.docx/prototype-links-collection.docx/g' "$temp_dir/index.html"
        sed -i 's/原型链接集合\.md/prototype-links.md/g' "$temp_dir/index.html"
        log_info "✓ 更新index.html链接"
    fi
    
    # 更新protfolio.html中的链接
    if [[ -f "$temp_dir/protfolio.html" ]]; then
        sed -i 's/1\. 能源管理系统/1-energy-management-system/g' "$temp_dir/protfolio.html"
        sed -i 's/2\.金山云需求集/2-kingsoft-cloud-requirements/g' "$temp_dir/protfolio.html"
        sed -i 's/3\.优维科技/3-youwei-tech/g' "$temp_dir/protfolio.html"
        sed -i 's/4\.众邦银行/4-zhongbang-bank/g' "$temp_dir/protfolio.html"
        log_info "✓ 更新protfolio.html链接"
    fi
}

# 创建部署包
create_deploy_package() {
    log_step "创建部署包..."
    
    local temp_dir="$1"
    local package_name="website_deploy_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    # 创建tar包
    tar -czf "$package_name" -C "$temp_dir" .
    
    log_info "✓ 部署包创建完成: $package_name"
    echo "$package_name"
}

# 生成部署说明
generate_deploy_instructions() {
    local package_name="$1"
    local instructions_file="部署说明.txt"
    
    cat > "$instructions_file" << EOF
网站部署说明
==============

部署包: $package_name

服务器部署步骤:
1. 上传部署包到服务器
   scp $package_name user@server:/tmp/

2. 登录服务器并解压
   ssh user@server
   cd /tmp
   tar -xzf $package_name

3. 运行部署脚本
   sudo ./simple-deploy.sh

4. 或者手动部署:
   sudo mkdir -p /var/www/portfolio
   sudo cp -r * /var/www/portfolio/
   sudo chown -R www-data:www-data /var/www/portfolio
   sudo systemctl restart nginx

文件名映射:
- 个人简历-张一钦.pdf -> resume-zhang-yiqin.pdf
- 原型链接合集.docx -> prototype-links-collection.docx
- 原型链接集合.md -> prototype-links.md
- 1. 能源管理系统 -> 1-energy-management-system
- 2.金山云需求集 -> 2-kingsoft-cloud-requirements
- 3.优维科技 -> 3-youwei-tech
- 4.众邦银行 -> 4-zhongbang-bank

注意事项:
1. 所有中文文件名已转换为英文，避免编码问题
2. HTML文件中的链接已相应更新
3. 建议在服务器上设置UTF-8编码环境
4. 如需恢复原始文件名，请保留本地副本

访问测试:
部署完成后，通过浏览器访问网站，确认所有链接正常工作。
EOF

    log_info "✓ 部署说明已生成: $instructions_file"
}

# 清理临时文件
cleanup() {
    if [[ -n "${TEMP_DIR:-}" && -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
        log_info "✓ 清理临时文件"
    fi
}

# 显示结果
show_results() {
    local package_name="$1"
    
    echo ""
    echo -e "${GREEN}========================================================"
    echo "                 打包完成！"
    echo "========================================================${NC}"
    echo ""
    echo "生成的文件:"
    echo "- 部署包: $package_name"
    echo "- 说明文档: 部署说明.txt"
    echo ""
    echo "下一步操作:"
    echo "1. 将部署包上传到服务器"
    echo "2. 在服务器上解压并部署"
    echo "3. 测试网站访问"
    echo ""
    echo "上传命令示例:"
    echo "scp $package_name user@your-server:/tmp/"
    echo ""
    echo "服务器部署命令:"
    echo "cd /tmp && tar -xzf $package_name && sudo ./simple-deploy.sh"
    echo ""
}

# 主函数
main() {
    show_banner
    
    log_info "开始重新打包网站文件..."
    
    # 检查必要文件
    if [[ ! -f "index.html" ]]; then
        log_error "缺少index.html文件"
        exit 1
    fi
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 创建安全文件名版本
    TEMP_DIR=$(create_safe_filenames)
    
    # 创建部署包
    package_name=$(create_deploy_package "$TEMP_DIR")
    
    # 生成部署说明
    generate_deploy_instructions "$package_name"
    
    # 显示结果
    show_results "$package_name"
    
    log_info "重新打包完成！"
}

# 显示帮助
show_help() {
    echo "重新打包部署工具"
    echo ""
    echo "功能:"
    echo "- 将中文文件名转换为英文"
    echo "- 更新HTML文件中的链接引用"
    echo "- 创建可直接部署的压缩包"
    echo "- 生成详细的部署说明"
    echo ""
    echo "使用方法:"
    echo "  $0              # 开始打包"
    echo "  $0 --help       # 显示帮助"
    echo ""
    echo "解决的问题:"
    echo "- 服务器中文文件名乱码"
    echo "- 文件链接失效"
    echo "- 编码不兼容"
    echo ""
}

# 处理参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
