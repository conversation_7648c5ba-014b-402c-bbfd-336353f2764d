<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>张一钦 - 产品经理简历导航</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: var(--shadow);
        }
        
        .profile {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }
        
        .avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 4px solid white;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--primary-color);
            box-shadow: var(--shadow);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .title {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: center;
            margin-top: 1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .main-content {
            padding: 3rem 0;
        }
        
        section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }
        
        h2 {
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
            display: inline-block;
        }
        
        .intro {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
        }
        
        .btn-download {
            background-color: var(--accent-color);
            margin-right: 1rem;
        }
        
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 1.5rem;
        }
        
        .project-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            transition: var(--transition);
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #eee;
        }
        
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        .project-content {
            padding: 1.5rem;
        }
        
        .project-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
        }
        
        .project-desc {
            color: #666;
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }
        
        .project-link {
            display: inline-block;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .project-link:hover {
            text-decoration: underline;
        }
        
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .skill-category {
            flex: 1;
            min-width: 250px;
        }
        
        .skill-category h3 {
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }
        
        .skill-list {
            list-style-type: none;
        }
        
        .skill-list li {
            background-color: var(--light-color);
            margin-bottom: 0.8rem;
            padding: 0.8rem;
            border-radius: 5px;
            border-left: 4px solid var(--primary-color);
        }
        
        footer {
            background-color: var(--dark-color);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 2rem;
        }
        
        /* 新增样式 */
        .tag {
            display: inline-block;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .section-divider {
            margin-bottom: 3rem;
        }

        .highlight-box {
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
            margin: 1.5rem 0;
        }

        .career-timeline {
            margin-top: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 10px;
        }

        .career-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .contact-section {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: white;
            padding: 3rem 0;
            margin-top: 3rem;
        }

        .contact-card {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .projects-grid {
                grid-template-columns: 1fr;
            }

            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 0.8rem;
            }

            .avatar {
                width: 120px;
                height: 120px;
                font-size: 2rem;
            }

            h1 {
                font-size: 2rem;
            }

            .skills-container {
                flex-direction: column;
            }

            .skill-category {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="profile">
                <div class="avatar">张</div>
                <h1>张一钦</h1>
                <p class="title">产品经理 | 云计算与AI应用专家</p>
                <p style="margin-top: 1rem; font-size: 1rem; opacity: 0.9;">6年技术研发 + 4年产品管理 | 专注企业级B端产品</p>
                <div class="contact-info">
                    <div class="contact-item">📧 <EMAIL></div>
                    <div class="contact-item">📱 18062604712</div>
                    <div class="contact-item">📍 武汉</div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <section id="about">
                <h2>个人简介</h2>
                <p class="intro">
                    6年技术研发与4年产品管理复合背景的产品人，专注于企业级B端产品，尤其在云计算与智能运维领域积累了丰富的实践经验。
                    擅长通过用户访谈、数据分析等方式挖掘客户核心痛点，将业务需求转化为可落地的产品方案。
                </p>
                <div style="margin-top: 2rem;">
                    <a href="resume-zhang-yiqin.pdf" class="btn btn-download" download>📄 下载完整简历(PDF)</a>
                    <a href="prototype-links.md" class="btn" download>📋 下载原型链接集合</a>
                    <a href="prototype-links-collection.docx" class="btn" download>📋 下载作品集文档</a>
                </div>
                <div style="margin-top: 1.5rem; padding: 1.5rem; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                    <h3 style="margin-bottom: 1rem; color: var(--secondary-color);">💡 关于这个导航页</h3>
                    <p style="margin: 0; color: #666; line-height: 1.6;">
                        这是我的个人简历导航页面，汇集了我在产品管理领域的主要作品和项目经验。
                        您可以通过下方的分类链接查看具体的原型设计、需求文档和实际应用案例。
                        如需了解更多详情，欢迎通过页面底部的联系方式与我沟通。
                    </p>
                </div>
            </section>

            <section id="projects">
                <h2>🚀 项目作品集</h2>
                <p style="margin-bottom: 2rem; color: #666; font-size: 1.1rem;">
                    以下是我在不同领域的产品设计和管理经验，重点展示AI应用、云监控、运维管理等前沿技术方向
                </p>

                <!-- AI生成的前端演示系统 -->
                <div style="margin-bottom: 2.5rem;">
                    <h3 style="color: var(--secondary-color); margin-bottom: 1.5rem; padding-bottom: 0.5rem; border-bottom: 2px solid #4caf50;">⚡ AI生成的前端演示系统</h3>
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">智能能源管理平台</h4>
                                <p class="project-desc">使用VSCode + Augment AI自动生成的能源管理系统前端界面，包含虚拟电厂、负荷控制、能效分析等功能模块</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">智能电网</span>
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">AI生成</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">碳中和</span>
                                </div>
                                <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px; font-size: 0.9rem;">
                                    <strong>🔑 登录信息：</strong><br>
                                    用户名：admin<br>
                                    密码：123456
                                </div>
                                <a href="http://118.178.228.37:3003/" class="project-link" target="_blank">🌐 访问演示系统 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">慢性病管理系统</h4>
                                <p class="project-desc">使用VSCode + Augment AI自动生成的慢性病管理系统前端界面，支持患者管理、病情跟踪、用药提醒等功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">医疗健康</span>
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">AI生成</span>
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">患者管理</span>
                                </div>
                                <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px; font-size: 0.9rem;">
                                    <strong>🔑 登录信息：</strong><br>
                                    用户名：admin<br>
                                    密码：123456
                                </div>
                                <a href="http://118.178.228.37:5173/" class="project-link" target="_blank">🌐 访问演示系统 →</a>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 2rem; padding: 1.5rem; background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-radius: 10px; border-left: 4px solid #4caf50;">
                        <h4 style="color: #2e7d32; margin-bottom: 1rem;">💡 AI辅助开发说明</h4>
                        <p style="color: #2e7d32; margin: 0; line-height: 1.6;">
                            以上两个系统均为本人使用 <strong>VSCode + Augment AI</strong> 工具自动生成的前端演示界面，
                            展示了AI在产品原型快速实现中的应用能力。系统已部署到个人服务器，
                            可直接访问体验完整的交互流程和功能模块。
                        </p>
                    </div>
                </div>

                <!-- AI智能体应用 -->
                <div style="margin-bottom: 2.5rem;">
                    <h3 style="color: var(--secondary-color); margin-bottom: 1.5rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ff9800;">🤖 AI智能体应用</h3>
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">医疗问诊助手</h4>
                                <p class="project-desc">基于扣子平台开发的智能医疗问诊助手，支持症状分析、初步诊断建议、就医指导等功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">医疗AI</span>
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">问诊助手</span>
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">扣子平台</span>
                                </div>
                                <a href="https://www.coze.cn/store/agent/7546178287161737279?bot_id=true" class="project-link" target="_blank">🔗 体验智能体 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">企业办公助手</h4>
                                <p class="project-desc">面向企业办公场景的AI助手，支持文档处理、会议纪要、邮件撰写、工作计划等办公自动化功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">办公助手</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">自动化</span>
                                    <span style="background: #e1f5fe; color: #0277bd; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">企业级</span>
                                </div>
                                <a href="https://www.coze.cn/store/agent/7544664687280078882?bot_id=true" class="project-link" target="_blank">🔗 体验智能体 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">医疗分诊助手</h4>
                                <p class="project-desc">智能医疗分诊系统，根据患者症状描述自动推荐合适的科室和医生，提升医疗服务效率</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">医疗分诊</span>
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">智能推荐</span>
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">效率提升</span>
                                </div>
                                <a href="https://www.coze.cn/store/agent/7546178287161737279?bot_id=true" class="project-link" target="_blank">🔗 体验智能体 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">滴滴计费解答</h4>
                                <p class="project-desc">专门针对滴滴出行计费问题的AI客服助手，支持费用查询、计费规则解释、争议处理等功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">客服AI</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">计费解答</span>
                                    <span style="background: #e1f5fe; color: #0277bd; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">垂直领域</span>
                                </div>
                                <a href="https://www.coze.cn/store/agent/7543558438652379146?bot_id=true" class="project-link" target="_blank">🔗 体验智能体 →</a>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 2rem; padding: 1.5rem; background: linear-gradient(135deg, #fff3e0, #ffcc02); border-radius: 10px; border-left: 4px solid #ff9800;">
                        <h4 style="color: #e65100; margin-bottom: 1rem;">🚀 AI智能体开发经验</h4>
                        <p style="color: #e65100; margin: 0; line-height: 1.6;">
                            基于<strong>扣子(Coze)平台</strong>开发的多个垂直领域AI智能体，涵盖医疗健康、企业办公、客户服务等场景。
                            通过深度理解业务需求，设计合适的对话流程和知识库，实现了高质量的AI产品落地应用。
                            展示了从需求分析到产品实现的完整AI产品开发能力。
                        </p>
                    </div>
                </div>

                <!-- 云监控系统 -->
                <div style="margin-bottom: 3rem;">
                    <h3 style="color: var(--secondary-color); margin-bottom: 1.5rem; padding-bottom: 0.5rem; border-bottom: 2px solid #e74c3c;">☁️ 云监控系统原型</h3>
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">GPU事件管理系统</h4>
                                <p class="project-desc">云监控GPU事件管理原型，包含事件监控、告警处理、性能分析等功能模块</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">GPU监控</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">事件管理</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/2uwzkw/preview/xrxh9f" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">RDMA网络监控</h4>
                                <p class="project-desc">高性能网络监控系统，支持RDMA协议监控、网络拓扑可视化、性能指标分析</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">网络监控</span>
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">RDMA</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/gtkmzz/preview/d89m0k" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">EPC事件收敛系统</h4>
                                <p class="project-desc">事件处理中心，支持多源事件收敛、智能去重、告警升级等企业级事件管理功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">事件收敛</span>
                                    <span style="background: #e1f5fe; color: #0277bd; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">智能去重</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/sg0t4a/preview/1sw5n5" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">IB网络事件详情</h4>
                                <p class="project-desc">InfiniBand网络事件详情页面，提供网络故障诊断、性能分析、拓扑展示等功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">IB网络</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">故障诊断</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/p1ibh7/preview/k4d9dj" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">监控图表优化</h4>
                                <p class="project-desc">监控Dashboard图表优化设计，提升数据可视化效果和用户交互体验</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">数据可视化</span>
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">UX优化</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/5u9yr0/preview/27kyhq" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">四维图新告警优化</h4>
                                <p class="project-desc">针对四维图新业务场景的告警系统优化，提升告警准确性和处理效率</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">告警优化</span>
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">业务定制</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/htvyoi/preview/zv65dj" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 内部监控系统 -->
                <div style="margin-bottom: 3rem;">
                    <h3 style="color: var(--secondary-color); margin-bottom: 1.5rem; padding-bottom: 0.5rem; border-bottom: 2px solid #9c27b0;">🔧 内部监控系统原型</h3>
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">Pingmesh探测功能</h4>
                                <p class="project-desc">网络连通性探测系统，支持全网拓扑探测、延迟监控、丢包分析等网络质量评估功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">网络探测</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">质量监控</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/vs49h5/preview/4nyd98" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">SLI指标优化</h4>
                                <p class="project-desc">服务级别指标(SLI)优化设计，提供更精准的服务质量度量和SLA管理功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">SLI/SLA</span>
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">服务质量</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/iqenno/preview/s51kt0" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">事件中心管理</h4>
                                <p class="project-desc">统一事件管理中心，支持事件分类、优先级管理、处理流程跟踪等企业级事件处理功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">事件管理</span>
                                    <span style="background: #e1f5fe; color: #0277bd; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">流程跟踪</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/o3eszm/preview/ba2nc2" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">北斗告警系统</h4>
                                <p class="project-desc">北斗事件告警配置和菜单系统优化，提供更直观的告警配置和管理界面</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">告警配置</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">系统优化</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/j1ncfw/preview/ea04a9" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">日志告警系统</h4>
                                <p class="project-desc">日志告警定时任务和聚合功能，支持智能日志分析、告警聚合、批量操作等功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">日志分析</span>
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">智能聚合</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/vzmhrs/preview/q2eutl" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">日志查询优化</h4>
                                <p class="project-desc">日志查询操作流程优化设计（MasterGo），提升日志检索效率和用户体验</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">MasterGo</span>
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">流程优化</span>
                                </div>
                                <a href="https://mastergo.com/goto/MrYwaTRD?page_id=M&layer_id=1:4555&proto=1&shared=true" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 运维系统 -->
                <div style="margin-bottom: 3rem;">
                    <h3 style="color: var(--secondary-color); margin-bottom: 1.5rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ff5722;">⚙️ 运维系统原型</h3>
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">服务可观测项目</h4>
                                <p class="project-desc">企业级服务可观测性平台，支持分布式链路追踪、性能监控、故障诊断等功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e3f2fd; color: #1976d2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">可观测性</span>
                                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">链路追踪</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/fesc2e/preview/y28ejs" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">报警2.0升级</h4>
                                <p class="project-desc">新一代告警系统升级设计，支持智能告警、多渠道通知、告警收敛等高级功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #e8f5e8; color: #2e7d32; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">告警升级</span>
                                    <span style="background: #fff3e0; color: #f57c00; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">智能化</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/l75qp1/preview/wgz5hs" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">手机端告警屏蔽</h4>
                                <p class="project-desc">移动端告警快速屏蔽功能，支持一键屏蔽、批量操作、临时静默等移动化运维功能</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">移动端</span>
                                    <span style="background: #e1f5fe; color: #0277bd; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">快速操作</span>
                                </div>
                                <a href="https://app.axure.cloud/app/project/qxstdv/preview/yixzlx" class="project-link" target="_blank">🔗 查看原型 →</a>
                            </div>
                        </div>

                        <div class="project-card">
                            <div class="project-content">
                                <h4 class="project-title">众邦银行运维平台</h4>
                                <p class="project-desc">蓝鲸自动化运维平台设计，包含CI/CD流程、监控告警、自动化部署等金融级运维解决方案</p>
                                <div style="margin-top: 1rem;">
                                    <span style="background: #fce4ec; color: #c2185b; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem; margin-right: 0.5rem;">金融行业</span>
                                    <span style="background: #e1f5fe; color: #0277bd; padding: 0.3rem 0.6rem; border-radius: 4px; font-size: 0.8rem;">自动化运维</span>
                                </div>
                                <a href="prototype-links.md" class="project-link" download>📋 查看设计文档 →</a>
                            </div>
                        </div>
                    </div>
                </div>


            </section>

            <section id="skills">
                <h2>💼 专业技能与经验</h2>
                <div class="skills-container">
                    <div class="skill-category">
                        <h3>🎯 产品管理核心能力</h3>
                        <ul class="skill-list">
                            <li><strong>需求分析：</strong>用户访谈、数据分析、竞品分析、KANO模型应用</li>
                            <li><strong>产品设计：</strong>原型设计（Axure, MasterGo）、交互设计、用户体验优化</li>
                            <li><strong>项目管理：</strong>敏捷开发（Scrum/Kanban）、MVP实践、跨团队协作</li>
                            <li><strong>数据驱动：</strong>产品数据分析、A/B测试、用户行为分析</li>
                        </ul>
                    </div>

                    <div class="skill-category">
                        <h3>🤖 AI与新技术应用</h3>
                        <ul class="skill-list">
                            <li><strong>AI产品设计：</strong>大语言模型(LLM)应用、智能体开发、AI功能落地</li>
                            <li><strong>技术理解：</strong>机器学习基础、深度学习概念、AI产品架构</li>
                            <li><strong>实践经验：</strong>医疗AI、办公AI、客服AI等多场景应用</li>
                        </ul>
                    </div>

                    <div class="skill-category">
                        <h3>⚙️ 技术背景与工具</h3>
                        <ul class="skill-list">
                            <li><strong>系统运维：</strong>Linux系统、Shell/Python脚本、Docker容器化</li>
                            <li><strong>监控体系：</strong>Zabbix、Prometheus、ELK、Grafana</li>
                            <li><strong>DevOps：</strong>CI/CD流程设计（Jenkins、蓝盾）、自动化部署</li>
                            <li><strong>云计算：</strong>云原生架构理解、微服务设计思维</li>
                        </ul>
                    </div>
                </div>

                <!-- 工作经历亮点 -->
                <div style="margin-top: 3rem; padding: 2rem; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 10px;">
                    <h3 style="color: var(--secondary-color); margin-bottom: 1.5rem; text-align: center;">📈 职业发展历程</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                        <div style="background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">技术研发阶段</h4>
                            <p style="color: #666; font-size: 0.9rem; margin: 0;">6年技术开发经验，深度理解系统架构、运维监控、自动化部署等技术领域</p>
                        </div>
                        <div style="background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">产品管理阶段</h4>
                            <p style="color: #666; font-size: 0.9rem; margin: 0;">4年产品管理经验，专注B端企业级产品，擅长将技术能力转化为产品价值</p>
                        </div>
                        <div style="background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">AI应用探索</h4>
                            <p style="color: #666; font-size: 0.9rem; margin: 0;">积极探索AI技术在产品中的应用，开发多个智能体应用，关注AI产品化落地</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 联系方式部分 -->
    <section style="background: linear-gradient(135deg, var(--secondary-color), var(--primary-color)); color: white; padding: 2rem 0; margin-top: 1.5rem;">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: 1.5rem; color: white;">📞 联系方式</h2>
            <div style="display: flex; justify-content: center; align-items: center; gap: 3rem; flex-wrap: wrap; max-width: 900px; margin: 0 auto;">
                <div style="display: flex; align-items: center; gap: 0.8rem; padding: 1rem 1.5rem; background: rgba(255,255,255,0.1); border-radius: 50px; backdrop-filter: blur(10px);">
                    <div style="font-size: 1.5rem;">📧</div>
                    <div>
                        <span style="font-weight: 600; margin-right: 0.5rem;">邮箱:</span>
                        <span style="opacity: 0.9;"><EMAIL></span>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 0.8rem; padding: 1rem 1.5rem; background: rgba(255,255,255,0.1); border-radius: 50px; backdrop-filter: blur(10px);">
                    <div style="font-size: 1.5rem;">📱</div>
                    <div>
                        <span style="font-weight: 600; margin-right: 0.5rem;">电话:</span>
                        <span style="opacity: 0.9;">18062604712</span>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 0.8rem; padding: 1rem 1.5rem; background: rgba(255,255,255,0.1); border-radius: 50px; backdrop-filter: blur(10px);">
                    <div style="font-size: 1.5rem;">📍</div>
                    <div>
                        <span style="font-weight: 600; margin-right: 0.5rem;">地点:</span>
                        <span style="opacity: 0.9;">武汉（可远程）</span>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.2);">
                <p style="margin: 0; opacity: 0.8; font-size: 1.1rem;">
                    💼 期待与您探讨产品管理、AI应用、云计算等领域的合作机会
                </p>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>© 2025 张一钦 - 产品经理简历导航页</p>
            <p style="margin-top: 0.5rem; opacity: 0.8;">本页面展示了我在产品管理领域的主要作品和经验，详细简历请下载PDF版本</p>
        </div>
    </footer>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const projectCards = document.querySelectorAll('.project-card');
            
            projectCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    if (targetId !== '#') {
                        document.querySelector(targetId).scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>