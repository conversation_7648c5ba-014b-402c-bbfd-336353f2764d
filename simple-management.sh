#!/bin/bash

# 简化版网站管理工具
# 用途: 简单的网站管理操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
WEB_ROOT="/var/www/portfolio"
BACKUP_DIR="/var/backups/portfolio"
LOG_DIR="/var/log/portfolio"

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "========================================"
    echo "         简化版网站管理工具"
    echo "========================================"
    echo -e "${NC}"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1. 更新网站"
    echo "2. 查看网站状态"
    echo "3. 查看访问日志"
    echo "4. 备份网站"
    echo "5. 重启Nginx"
    echo "6. 查看系统信息"
    echo "0. 退出"
    echo ""
    read -p "请输入选项 (0-6): " choice
}

# 更新网站
update_website() {
    echo -e "${BLUE}更新网站${NC}"
    echo ""
    
    if [[ -f "./更新网站.sh" ]]; then
        ./更新网站.sh
    else
        log_error "未找到更新脚本"
        echo "请确保 更新网站.sh 在当前目录"
    fi
}

# 查看网站状态
show_status() {
    echo -e "${BLUE}网站状态${NC}"
    echo ""
    
    echo "=== Nginx状态 ==="
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✓ Nginx运行正常${NC}"
        systemctl status nginx --no-pager -l | head -10
    else
        echo -e "${RED}✗ Nginx未运行${NC}"
    fi
    
    echo ""
    echo "=== 网站文件 ==="
    if [[ -d $WEB_ROOT ]]; then
        echo "网站目录: $WEB_ROOT"
        ls -la $WEB_ROOT | head -10
    else
        log_warn "网站目录不存在"
    fi
    
    echo ""
    echo "=== 磁盘使用 ==="
    df -h $WEB_ROOT 2>/dev/null || df -h /
    
    echo ""
    echo "=== 内存使用 ==="
    free -h
}

# 查看访问日志
show_logs() {
    echo -e "${BLUE}访问日志${NC}"
    echo ""
    
    echo "请选择:"
    echo "1. 最近访问记录 (50条)"
    echo "2. 实时访问日志"
    echo "3. 访问统计"
    echo ""
    read -p "请选择 (1-3): " log_choice
    
    case $log_choice in
        1)
            if [[ -f "$LOG_DIR/access.log" ]]; then
                echo "最近50条访问记录:"
                tail -50 "$LOG_DIR/access.log"
            else
                log_warn "访问日志文件不存在"
            fi
            ;;
        2)
            if [[ -f "$LOG_DIR/access.log" ]]; then
                echo "实时访问日志 (按Ctrl+C退出):"
                tail -f "$LOG_DIR/access.log"
            else
                log_warn "访问日志文件不存在"
            fi
            ;;
        3)
            if [[ -f "$LOG_DIR/access.log" ]]; then
                echo "=== 访问统计 ==="
                echo "总访问次数: $(wc -l < "$LOG_DIR/access.log")"
                echo ""
                echo "=== 热门页面 ==="
                awk '{print $7}' "$LOG_DIR/access.log" | sort | uniq -c | sort -nr | head -5
                echo ""
                echo "=== 访问IP统计 ==="
                awk '{print $1}' "$LOG_DIR/access.log" | sort | uniq -c | sort -nr | head -5
            else
                log_warn "访问日志文件不存在"
            fi
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 备份网站
backup_website() {
    echo -e "${BLUE}备份网站${NC}"
    echo ""
    
    if [[ ! -d $WEB_ROOT ]]; then
        log_error "网站目录不存在"
        return 1
    fi
    
    backup_name="manual_backup_$(date +%Y%m%d_%H%M%S)"
    backup_path="$BACKUP_DIR/$backup_name"
    
    mkdir -p "$backup_path"
    cp -r $WEB_ROOT/* "$backup_path/"
    
    # 压缩备份
    cd $BACKUP_DIR
    tar -czf "${backup_name}.tar.gz" "$backup_name"
    rm -rf "$backup_name"
    
    log_info "备份完成: ${backup_path}.tar.gz"
    
    echo ""
    echo "现有备份:"
    ls -la $BACKUP_DIR/*.tar.gz 2>/dev/null | tail -5 || echo "无备份文件"
}

# 重启Nginx
restart_nginx() {
    echo -e "${BLUE}重启Nginx${NC}"
    echo ""
    
    echo "请选择操作:"
    echo "1. 重新加载配置"
    echo "2. 重启Nginx服务"
    echo ""
    read -p "请选择 (1-2): " restart_choice
    
    case $restart_choice in
        1)
            if nginx -t; then
                systemctl reload nginx
                log_info "Nginx配置已重新加载"
            else
                log_error "Nginx配置有误，请检查"
            fi
            ;;
        2)
            systemctl restart nginx
            if systemctl is-active --quiet nginx; then
                log_info "Nginx重启成功"
            else
                log_error "Nginx重启失败"
            fi
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 查看系统信息
show_system_info() {
    echo -e "${BLUE}系统信息${NC}"
    echo ""
    
    echo "=== 系统基本信息 ==="
    uname -a
    echo ""
    
    echo "=== 系统负载 ==="
    uptime
    echo ""
    
    echo "=== 磁盘使用 ==="
    df -h
    echo ""
    
    echo "=== 内存使用 ==="
    free -h
    echo ""
    
    echo "=== 网络端口 ==="
    ss -tuln | grep :80 || echo "端口80未监听"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 主函数
main() {
    check_permissions
    show_banner
    
    while true; do
        show_menu
        
        case $choice in
            1) update_website ;;
            2) show_status ;;
            3) show_logs ;;
            4) backup_website ;;
            5) restart_nginx ;;
            6) show_system_info ;;
            0) 
                log_info "退出管理工具"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..." -r
    done
}

# 显示帮助
show_help() {
    echo "简化版网站管理工具"
    echo ""
    echo "功能:"
    echo "- 更新网站内容"
    echo "- 查看网站状态"
    echo "- 查看访问日志"
    echo "- 备份网站数据"
    echo "- 重启Nginx服务"
    echo "- 查看系统信息"
    echo ""
    echo "使用方法:"
    echo "  $0              # 启动管理工具"
    echo "  $0 --help       # 显示帮助"
}

# 处理参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
