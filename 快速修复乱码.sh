#!/bin/bash

# 快速修复服务器文件名乱码脚本
# 直接在服务器上运行，快速解决中文文件名乱码问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
WEB_ROOT="/var/www/portfolio"
BACKUP_DIR="/var/backups/portfolio"

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要root权限运行"
        echo "使用: sudo $0"
        exit 1
    fi
}

# 快速修复函数
quick_fix() {
    log_step "开始快速修复文件名乱码..."
    
    # 创建备份
    backup_name="quick_fix_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR/$backup_name"
    if [[ -d "$WEB_ROOT" ]]; then
        cp -r "$WEB_ROOT"/* "$BACKUP_DIR/$backup_name/" 2>/dev/null || true
        log_info "已备份到: $BACKUP_DIR/$backup_name"
    fi
    
    cd "$WEB_ROOT" || exit 1
    
    # 预定义的文件名映射
    declare -A renames=(
        # 根据您的截图，这些是常见的乱码模式
        ["个人简历-张一钦.pdf"]="resume-zhang-yiqin.pdf"
        ["原型链接合集.docx"]="prototype-links.docx"
        ["原型链接集合.md"]="prototype-links.md"
        ["更新网站.sh"]="update-website.sh"
        ["简化管理.sh"]="simple-management.sh"
        ["简化部署.sh"]="simple-deploy.sh"
    )
    
    # 执行重命名
    for pattern in *; do
        if [[ -f "$pattern" || -d "$pattern" ]]; then
            # 检查是否包含非ASCII字符
            if [[ "$pattern" =~ [^[:ascii:]] ]]; then
                # 生成安全的文件名
                safe_name=$(echo "$pattern" | iconv -f UTF-8 -t ASCII//TRANSLIT 2>/dev/null | tr -cd '[:alnum:]._-' | head -c 50)
                
                # 如果转换失败，使用时间戳
                if [[ -z "$safe_name" || "$safe_name" == "." ]]; then
                    extension="${pattern##*.}"
                    if [[ "$extension" == "$pattern" ]]; then
                        safe_name="file_$(date +%s)_$RANDOM"
                    else
                        safe_name="file_$(date +%s)_$RANDOM.$extension"
                    fi
                fi
                
                # 确保文件名唯一
                counter=1
                original_safe_name="$safe_name"
                while [[ -e "$safe_name" && "$safe_name" != "$pattern" ]]; do
                    if [[ "$original_safe_name" =~ \. ]]; then
                        name_part="${original_safe_name%.*}"
                        ext_part="${original_safe_name##*.}"
                        safe_name="${name_part}_${counter}.${ext_part}"
                    else
                        safe_name="${original_safe_name}_${counter}"
                    fi
                    ((counter++))
                done
                
                # 执行重命名
                if mv "$pattern" "$safe_name" 2>/dev/null; then
                    log_info "✓ 重命名: $pattern -> $safe_name"
                else
                    log_warn "✗ 重命名失败: $pattern"
                fi
            fi
        fi
    done
    
    # 修复权限
    chown -R www-data:www-data . 2>/dev/null || chown -R nginx:nginx .
    find . -type d -exec chmod 755 {} \;
    find . -type f -exec chmod 644 {} \;
    
    log_info "权限修复完成"
}

# 更新HTML链接
update_html_links() {
    log_step "更新HTML文件链接..."
    
    cd "$WEB_ROOT" || return
    
    # 查找所有HTML文件并更新链接
    for html_file in *.html; do
        if [[ -f "$html_file" ]]; then
            # 备份HTML文件
            cp "$html_file" "${html_file}.backup"
            
            # 更新常见的中文链接
            sed -i 's/个人简历-张一钦\.pdf/resume-zhang-yiqin.pdf/g' "$html_file" 2>/dev/null || true
            sed -i 's/原型链接合集\.docx/prototype-links.docx/g' "$html_file" 2>/dev/null || true
            sed -i 's/原型链接集合\.md/prototype-links.md/g' "$html_file" 2>/dev/null || true
            
            log_info "✓ 更新HTML文件: $html_file"
        fi
    done
}

# 重启服务
restart_services() {
    log_step "重启Web服务..."
    
    # 测试Nginx配置
    if nginx -t 2>/dev/null; then
        systemctl reload nginx
        log_info "✓ Nginx重新加载成功"
    else
        log_warn "Nginx配置测试失败，尝试重启"
        systemctl restart nginx
    fi
    
    if systemctl is-active --quiet nginx; then
        log_info "✓ Nginx运行正常"
    else
        log_error "✗ Nginx启动失败"
    fi
}

# 验证修复
verify_fix() {
    log_step "验证修复结果..."
    
    cd "$WEB_ROOT" || return
    
    # 检查是否还有乱码文件
    garbled_count=0
    for file in *; do
        if [[ -e "$file" && "$file" =~ [^[:ascii:]] ]]; then
            ((garbled_count++))
            log_warn "仍有乱码: $file"
        fi
    done
    
    if [[ $garbled_count -eq 0 ]]; then
        log_info "✓ 所有文件名已修复"
    else
        log_warn "仍有 $garbled_count 个文件存在编码问题"
    fi
    
    # 显示当前文件列表
    echo ""
    echo "当前文件列表:"
    ls -la | grep -v "^total" | awk '{print $9}' | grep -v "^\.$" | grep -v "^\.\.$"
}

# 显示结果
show_results() {
    echo ""
    echo -e "${GREEN}========================================"
    echo "           快速修复完成！"
    echo "========================================${NC}"
    echo ""
    echo "修复内容:"
    echo "- 重命名乱码文件为ASCII字符"
    echo "- 更新HTML文件中的链接"
    echo "- 修复文件权限"
    echo "- 重启Web服务"
    echo ""
    echo "备份位置: $BACKUP_DIR"
    echo "网站目录: $WEB_ROOT"
    echo ""
    echo "如果需要恢复:"
    echo "sudo cp -r $BACKUP_DIR/quick_fix_*/\* $WEB_ROOT/"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}========================================"
    echo "        快速修复文件名乱码工具"
    echo "========================================${NC}"
    echo ""
    
    check_root
    
    if [[ ! -d "$WEB_ROOT" ]]; then
        log_error "网站目录不存在: $WEB_ROOT"
        exit 1
    fi
    
    quick_fix
    update_html_links
    restart_services
    verify_fix
    show_results
    
    log_info "快速修复完成！请测试网站访问是否正常。"
}

# 运行主函数
main "$@"
