#!/bin/bash

# 简化版个人网站部署脚本
# 用途: 快速部署个人简历网站（仅HTTP，无SSL）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
DOMAIN="your-domain.com"  # 请修改为您的域名，或使用IP地址
WEB_ROOT="/var/www/portfolio"
BACKUP_DIR="/var/backups/portfolio"

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "========================================================"
    echo "           个人网站简化部署脚本"
    echo "           Simple Portfolio Deploy Script"
    echo "========================================================"
    echo -e "${NC}"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    if [[ -f /etc/debian_version ]]; then
        OS="debian"
        log_info "检测到Debian/Ubuntu系统"
    elif [[ -f /etc/redhat-release ]]; then
        OS="redhat"
        log_info "检测到RedHat/CentOS系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
}

# 收集配置信息
collect_config() {
    log_step "配置网站信息..."
    
    echo ""
    echo "请选择访问方式:"
    echo "1. 使用域名访问 (需要域名解析)"
    echo "2. 使用IP地址访问 (直接使用服务器IP)"
    echo ""
    read -p "请选择 (1/2): " access_type
    
    case $access_type in
        1)
            read -p "请输入您的域名 (例: example.com): " DOMAIN
            if [[ -z "$DOMAIN" ]]; then
                log_error "域名不能为空"
                exit 1
            fi
            ;;
        2)
            DOMAIN=$(curl -s ifconfig.me || hostname -I | awk '{print $1}')
            log_info "将使用IP地址: $DOMAIN"
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
    
    log_info "网站将通过 http://$DOMAIN 访问"
}

# 安装必要软件
install_packages() {
    log_step "安装必要软件..."

    if [[ $OS == "debian" ]]; then
        apt update
        apt install -y nginx curl wget unzip rsync iconv
    elif [[ $OS == "redhat" ]]; then
        yum update -y
        yum install -y nginx curl wget unzip rsync glibc-common
    fi

    log_info "软件安装完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    mkdir -p $WEB_ROOT
    mkdir -p $BACKUP_DIR
    mkdir -p /var/log/portfolio
    
    log_info "目录创建完成"
}

# 配置Nginx（简化版）
configure_nginx() {
    log_step "配置Nginx..."
    
    # 备份默认配置
    if [[ -f /etc/nginx/sites-available/default ]]; then
        cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup
    fi
    
    # 创建简化的Nginx配置
    cat > /etc/nginx/sites-available/portfolio << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    root $WEB_ROOT;
    index index.html protfolio.html;
    
    # 基本安全设置
    server_tokens off;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|docx|md)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 主页面
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 访问日志
    access_log /var/log/portfolio/access.log;
    error_log /var/log/portfolio/error.log;
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/portfolio /etc/nginx/sites-enabled/
    
    # 禁用默认站点
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t
    
    log_info "Nginx配置完成"
}

# 检查和设置系统编码
setup_locale() {
    log_step "检查系统编码设置..."

    # 检查当前locale
    current_locale=$(locale | grep LANG= | cut -d= -f2)
    log_info "当前系统编码: $current_locale"

    # 确保系统支持UTF-8
    if [[ $OS == "debian" ]]; then
        # 安装中文语言包
        apt install -y locales language-pack-zh-hans

        # 生成UTF-8 locale
        locale-gen zh_CN.UTF-8
        locale-gen en_US.UTF-8

        # 更新locale配置
        update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

    elif [[ $OS == "redhat" ]]; then
        # 安装中文语言包
        yum install -y glibc-langpack-zh glibc-langpack-en

        # 设置系统locale
        localectl set-locale LANG=en_US.UTF-8
    fi

    # 设置当前会话的编码
    export LANG=en_US.UTF-8
    export LC_ALL=en_US.UTF-8

    log_info "系统编码设置完成"
}

# 处理中文文件名
handle_chinese_filenames() {
    log_step "处理中文文件名..."

    # 创建文件名映射
    declare -A filename_map

    # 扫描当前目录中的中文文件
    while IFS= read -r -d '' file; do
        if [[ "$file" =~ [[:space:]]*[\u4e00-\u9fff] ]]; then
            # 生成安全的英文文件名
            safe_name=$(echo "$file" | iconv -f UTF-8 -t ASCII//TRANSLIT 2>/dev/null || echo "$file" | tr -cd '[:alnum:]._-')
            if [[ -z "$safe_name" || "$safe_name" == "$file" ]]; then
                # 如果转换失败，使用时间戳
                extension="${file##*.}"
                safe_name="file_$(date +%s)_$RANDOM.$extension"
            fi
            filename_map["$file"]="$safe_name"
            log_info "文件映射: $file -> $safe_name"
        fi
    done < <(find . -maxdepth 1 -type f -print0)

    # 创建符号链接或重命名文件
    for original in "${!filename_map[@]}"; do
        safe_name="${filename_map[$original]}"
        if [[ -f "$original" && "$original" != "$safe_name" ]]; then
            # 创建符号链接，保持原文件
            ln -sf "$original" "$safe_name"
            log_info "创建链接: $safe_name -> $original"
        fi
    done
}

# 上传网站文件
upload_files() {
    log_step "部署网站文件..."

    # 处理中文文件名
    handle_chinese_filenames

    # 检查必要文件
    required_files=("index.html" "protfolio.html")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done

    # 备份现有文件
    if [[ -d $WEB_ROOT ]] && [[ "$(ls -A $WEB_ROOT)" ]]; then
        backup_name="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR/$backup_name"
        cp -r $WEB_ROOT/* "$BACKUP_DIR/$backup_name/" 2>/dev/null || true
        log_info "已备份到: $BACKUP_DIR/$backup_name"
    fi

    # 复制文件，使用rsync处理编码
    if command -v rsync &> /dev/null; then
        rsync -av --exclude='*.sh' --exclude='*.bat' ./ $WEB_ROOT/
    else
        # 逐个复制文件，确保编码正确
        find . -maxdepth 1 -type f ! -name "*.sh" ! -name "*.bat" -print0 | while IFS= read -r -d '' file; do
            filename=$(basename "$file")
            # 使用iconv确保文件名编码正确
            target_file="$WEB_ROOT/$filename"
            cp "$file" "$target_file" 2>/dev/null || true
        done

        # 复制目录
        find . -maxdepth 1 -type d ! -name "." -print0 | while IFS= read -r -d '' dir; do
            cp -r "$dir" "$WEB_ROOT/" 2>/dev/null || true
        done
    fi

    # 设置权限
    chown -R www-data:www-data $WEB_ROOT 2>/dev/null || chown -R nginx:nginx $WEB_ROOT
    find $WEB_ROOT -type d -exec chmod 755 {} \;
    find $WEB_ROOT -type f -exec chmod 644 {} \;

    log_info "网站文件部署完成"
}

# 配置防火墙
setup_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        ufw allow 80/tcp
        ufw allow ssh
        log_info "UFW防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-service=http
        firewall-cmd --reload
        log_info "Firewalld防火墙配置完成"
    else
        log_warn "未检测到防火墙，请手动开放80端口"
    fi
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    systemctl enable nginx
    systemctl restart nginx
    
    if systemctl is-active --quiet nginx; then
        log_info "Nginx启动成功"
    else
        log_error "Nginx启动失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 检查网站文件
    if [[ -f "$WEB_ROOT/index.html" ]]; then
        log_info "✓ 网站文件部署成功"
    else
        log_error "✗ 网站文件部署失败"
    fi
    
    # 检查HTTP访问
    sleep 2
    if curl -s "http://$DOMAIN" > /dev/null; then
        log_info "✓ 网站访问正常"
    else
        log_warn "✗ 网站访问可能有问题，请检查防火墙设置"
    fi
}

# 显示结果
show_results() {
    echo ""
    echo -e "${GREEN}========================================================"
    echo "                 部署完成！"
    echo "========================================================${NC}"
    echo ""
    echo "网站信息:"
    echo "- 访问地址: http://$DOMAIN"
    echo "- 网站目录: $WEB_ROOT"
    echo "- 备份目录: $BACKUP_DIR"
    echo "- 日志目录: /var/log/portfolio"
    echo ""
    echo "管理命令:"
    echo "- 重启Nginx: sudo systemctl restart nginx"
    echo "- 查看状态: sudo systemctl status nginx"
    echo "- 查看日志: sudo tail -f /var/log/portfolio/access.log"
    echo ""
    echo "更新网站:"
    echo "1. 修改本地文件"
    echo "2. 重新运行此脚本"
    echo ""
    if [[ "$DOMAIN" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo -e "${YELLOW}注意: 您使用的是IP地址访问，建议配置域名以获得更好的访问体验${NC}"
    fi
    echo ""
}

# 主函数
main() {
    show_banner
    
    log_info "开始简化部署..."
    
    check_root
    detect_system
    collect_config
    install_packages
    setup_locale
    create_directories
    configure_nginx
    upload_files
    setup_firewall
    start_services
    verify_deployment
    show_results
    
    log_info "简化部署完成！"
}

# 显示帮助
show_help() {
    echo "个人网站简化部署脚本"
    echo ""
    echo "特点:"
    echo "- 仅使用HTTP，无需SSL证书"
    echo "- 配置简单，快速部署"
    echo "- 适合个人简历展示网站"
    echo ""
    echo "使用方法:"
    echo "  $0              # 开始部署"
    echo "  $0 --help       # 显示帮助"
    echo ""
    echo "部署要求:"
    echo "- Linux服务器 (Ubuntu/CentOS)"
    echo "- Root权限"
    echo "- 开放80端口"
}

# 处理参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
