#!/bin/bash

# 修复服务器上中文文件名乱码问题
# 用途: 修复已部署网站的中文文件名编码问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
WEB_ROOT="/var/www/portfolio"
BACKUP_DIR="/var/backups/portfolio"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "========================================================"
    echo "           文件名编码修复工具"
    echo "           Filename Encoding Fix Tool"
    echo "========================================================"
    echo -e "${NC}"
}

# 检查权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 设置系统编码
setup_system_encoding() {
    log_step "设置系统编码..."
    
    # 检测系统类型
    if [[ -f /etc/debian_version ]]; then
        OS="debian"
        # 安装必要的编码工具
        apt update
        apt install -y locales language-pack-zh-hans convmv
        
        # 生成UTF-8 locale
        locale-gen zh_CN.UTF-8
        locale-gen en_US.UTF-8
        
    elif [[ -f /etc/redhat-release ]]; then
        OS="redhat"
        # 安装必要的编码工具
        yum install -y glibc-langpack-zh glibc-langpack-en
        
        # 设置系统locale
        localectl set-locale LANG=en_US.UTF-8
    fi
    
    # 设置当前会话的编码
    export LANG=en_US.UTF-8
    export LC_ALL=en_US.UTF-8
    
    log_info "系统编码设置完成"
}

# 检测乱码文件
detect_garbled_files() {
    log_step "检测乱码文件..."
    
    garbled_files=()
    
    # 查找可能的乱码文件
    while IFS= read -r -d '' file; do
        filename=$(basename "$file")
        # 检测是否包含乱码字符
        if [[ "$filename" =~ [^[:print:]] ]] || [[ "$filename" =~ \?+ ]]; then
            garbled_files+=("$file")
            log_warn "发现乱码文件: $file"
        fi
    done < <(find "$WEB_ROOT" -type f -print0 2>/dev/null)
    
    if [[ ${#garbled_files[@]} -eq 0 ]]; then
        log_info "未发现乱码文件"
        return 0
    fi
    
    log_info "共发现 ${#garbled_files[@]} 个乱码文件"
    return 1
}

# 创建文件映射表
create_filename_mapping() {
    log_step "创建文件名映射表..."
    
    # 创建映射文件
    mapping_file="/tmp/filename_mapping.txt"
    > "$mapping_file"
    
    echo "请为以下乱码文件提供正确的文件名："
    echo "格式: 原文件名 -> 新文件名"
    echo ""
    
    local counter=1
    for file in "${garbled_files[@]}"; do
        filename=$(basename "$file")
        echo "[$counter] 当前文件名: $filename"
        
        # 尝试自动猜测文件类型
        file_type=$(file -b "$file" 2>/dev/null || echo "unknown")
        echo "    文件类型: $file_type"
        
        # 根据文件类型建议名称
        case "$file_type" in
            *PDF*)
                suggested_name="document_$counter.pdf"
                ;;
            *HTML*)
                suggested_name="page_$counter.html"
                ;;
            *image*|*JPEG*|*PNG*)
                suggested_name="image_$counter.jpg"
                ;;
            *Word*)
                suggested_name="document_$counter.docx"
                ;;
            *)
                extension="${filename##*.}"
                suggested_name="file_$counter.$extension"
                ;;
        esac
        
        echo "    建议名称: $suggested_name"
        read -p "    请输入新文件名 (回车使用建议名称): " new_name
        
        if [[ -z "$new_name" ]]; then
            new_name="$suggested_name"
        fi
        
        echo "$file|$new_name" >> "$mapping_file"
        echo ""
        ((counter++))
    done
    
    log_info "文件名映射表创建完成: $mapping_file"
}

# 执行文件重命名
rename_files() {
    log_step "执行文件重命名..."
    
    mapping_file="/tmp/filename_mapping.txt"
    
    if [[ ! -f "$mapping_file" ]]; then
        log_error "映射文件不存在"
        return 1
    fi
    
    # 创建备份
    backup_name="encoding_fix_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR/$backup_name"
    cp -r "$WEB_ROOT"/* "$BACKUP_DIR/$backup_name/" 2>/dev/null || true
    log_info "已创建备份: $BACKUP_DIR/$backup_name"
    
    # 执行重命名
    while IFS='|' read -r old_file new_name; do
        if [[ -f "$old_file" ]]; then
            dir_path=$(dirname "$old_file")
            new_path="$dir_path/$new_name"
            
            # 确保新文件名不冲突
            counter=1
            original_new_path="$new_path"
            while [[ -f "$new_path" && "$new_path" != "$old_file" ]]; do
                name_without_ext="${original_new_path%.*}"
                extension="${original_new_path##*.}"
                new_path="${name_without_ext}_${counter}.${extension}"
                ((counter++))
            done
            
            # 执行重命名
            if mv "$old_file" "$new_path" 2>/dev/null; then
                log_info "✓ 重命名成功: $(basename "$old_file") -> $(basename "$new_path")"
            else
                log_error "✗ 重命名失败: $old_file"
            fi
        fi
    done < "$mapping_file"
    
    # 清理临时文件
    rm -f "$mapping_file"
}

# 更新Nginx配置中的文件引用
update_nginx_config() {
    log_step "检查Nginx配置..."
    
    nginx_config="/etc/nginx/sites-available/portfolio"
    
    if [[ -f "$nginx_config" ]]; then
        # 备份配置文件
        cp "$nginx_config" "${nginx_config}.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 重新加载Nginx配置
        nginx -t && systemctl reload nginx
        log_info "Nginx配置已重新加载"
    fi
}

# 验证修复结果
verify_fix() {
    log_step "验证修复结果..."
    
    # 再次检测乱码文件
    remaining_garbled=0
    while IFS= read -r -d '' file; do
        filename=$(basename "$file")
        if [[ "$filename" =~ [^[:print:]] ]] || [[ "$filename" =~ \?+ ]]; then
            ((remaining_garbled++))
            log_warn "仍有乱码文件: $file"
        fi
    done < <(find "$WEB_ROOT" -type f -print0 2>/dev/null)
    
    if [[ $remaining_garbled -eq 0 ]]; then
        log_info "✓ 所有文件名编码问题已修复"
    else
        log_warn "✗ 仍有 $remaining_garbled 个文件存在编码问题"
    fi
    
    # 列出当前所有文件
    echo ""
    echo "当前网站文件列表:"
    find "$WEB_ROOT" -type f -exec basename {} \; | sort
}

# 显示结果
show_results() {
    echo ""
    echo -e "${GREEN}========================================================"
    echo "                 修复完成！"
    echo "========================================================${NC}"
    echo ""
    echo "修复信息:"
    echo "- 网站目录: $WEB_ROOT"
    echo "- 备份目录: $BACKUP_DIR"
    echo ""
    echo "如果网站访问有问题，请检查:"
    echo "1. 文件权限是否正确"
    echo "2. Nginx配置是否需要更新"
    echo "3. 文件路径引用是否需要修改"
    echo ""
    echo "恢复备份命令:"
    echo "sudo cp -r $BACKUP_DIR/encoding_fix_backup_*/\* $WEB_ROOT/"
    echo ""
}

# 主函数
main() {
    show_banner
    
    log_info "开始修复文件名编码问题..."
    
    check_root
    setup_system_encoding
    
    if detect_garbled_files; then
        log_info "未发现编码问题，无需修复"
        exit 0
    fi
    
    create_filename_mapping
    rename_files
    update_nginx_config
    verify_fix
    show_results
    
    log_info "文件名编码修复完成！"
}

# 显示帮助
show_help() {
    echo "文件名编码修复工具"
    echo ""
    echo "功能:"
    echo "- 检测服务器上的乱码文件名"
    echo "- 提供交互式重命名功能"
    echo "- 自动备份原始文件"
    echo "- 更新相关配置"
    echo ""
    echo "使用方法:"
    echo "  $0              # 开始修复"
    echo "  $0 --help       # 显示帮助"
    echo ""
}

# 处理参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
