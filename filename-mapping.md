# 文件名映射说明

## 概述
为了解决服务器部署时中文文件名乱码问题，已将所有中文文件名改为英文文件名，并更新了相关的HTML链接引用。

## 文件名映射表

### 主要文档文件
| 原文件名 | 新文件名 | 文件类型 | 说明 |
|---------|---------|---------|------|
| 个人简历-张一钦.pdf | resume-zhang-yiqin.pdf | PDF文档 | 个人简历文件 |
| 原型链接合集.docx | prototype-links-collection.docx | Word文档 | 原型链接合集文档 |
| 原型链接集合.md | prototype-links.md | Markdown文档 | 原型链接集合文档 |

### 脚本文件
| 原文件名 | 新文件名 | 文件类型 | 说明 |
|---------|---------|---------|------|
| 更新网站.sh | update-website.sh | Shell脚本 | 网站更新脚本 |
| 简化管理.sh | simple-management.sh | Shell脚本 | 简化管理脚本 |
| 简化部署.sh | simple-deploy.sh | Shell脚本 | 简化部署脚本 |
| 修复文件名编码.sh | fix-filename-encoding.sh | Shell脚本 | 修复文件名编码脚本 |
| 快速修复乱码.sh | quick-fix-encoding.sh | Shell脚本 | 快速修复乱码脚本 |
| 重新打包部署.sh | repackage-deploy.sh | Shell脚本 | 重新打包部署脚本 |

## HTML文件更新

### index.html 更新内容
- 第332行：`个人简历-张一钦.pdf` → `resume-zhang-yiqin.pdf`
- 第333行：`原型链接集合.md` → `prototype-links.md`
- 第334行：`原型链接合集.docx` → `prototype-links-collection.docx`
- 第556行：`原型链接集合.md` → `prototype-links.md`

### protfolio.html 更新内容
- 第332行：`个人简历-张一钦.pdf` → `resume-zhang-yiqin.pdf`
- 第333行：`原型链接集合.md` → `prototype-links.md`
- 第334行：`原型链接合集.docx` → `prototype-links-collection.docx`
- 第556行：`原型链接集合.md` → `prototype-links.md`

## 部署优势

### 解决的问题
1. **编码兼容性**：避免中文文件名在Linux服务器上显示乱码
2. **链接有效性**：确保HTML文件中的下载链接正常工作
3. **跨平台兼容**：英文文件名在所有操作系统上都能正确显示
4. **URL友好**：英文文件名更适合Web环境

### 命名规范
- 使用小写字母和连字符（kebab-case）
- 避免特殊字符和空格
- 保持文件名简洁明了
- 保留原始文件的语义含义

## 验证清单

### 文件重命名验证
- [x] 个人简历PDF文件已重命名
- [x] 原型链接文档已重命名
- [x] 所有Shell脚本已重命名
- [x] 无中文文件名残留

### HTML链接验证
- [x] index.html中的PDF链接已更新
- [x] index.html中的文档链接已更新
- [x] protfolio.html中的PDF链接已更新
- [x] protfolio.html中的文档链接已更新

### 功能验证
- [ ] 本地测试所有下载链接正常
- [ ] 服务器部署后测试文件访问
- [ ] 确认文件名显示正常
- [ ] 验证下载功能正常

## 部署建议

### 立即部署
现在可以直接部署到服务器，不会再出现文件名乱码问题：

```bash
# 使用简化部署脚本
sudo bash simple-deploy.sh

# 或使用快速修复脚本（如果已部署）
sudo bash quick-fix-encoding.sh
```

### 未来维护
1. 新增文件时请使用英文文件名
2. 更新HTML链接时注意文件名一致性
3. 定期检查链接有效性
4. 保持文件名命名规范的一致性

## 回滚方案

如果需要恢复中文文件名（仅限本地开发环境）：

```bash
# 恢复主要文档文件
mv resume-zhang-yiqin.pdf "个人简历-张一钦.pdf"
mv prototype-links-collection.docx "原型链接合集.docx"
mv prototype-links.md "原型链接集合.md"

# 恢复脚本文件
mv update-website.sh "更新网站.sh"
mv simple-management.sh "简化管理.sh"
mv simple-deploy.sh "简化部署.sh"
```

**注意**：回滚后需要同时恢复HTML文件中的链接引用。

## 总结

通过这次文件名标准化，解决了：
- ✅ 服务器部署时的中文文件名乱码问题
- ✅ HTML链接失效问题
- ✅ 跨平台兼容性问题
- ✅ Web环境下的文件访问问题

现在可以安全地部署到任何Linux服务器，无需担心编码问题。
